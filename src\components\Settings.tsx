import React, { useState } from 'react';
import { Card, Form, InputNumber, Button, Typography, message } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import type { AppSettings } from '../types';

const { Title } = Typography;

interface SettingsProps {
  settings: AppSettings;
  onSettingsChange: (settings: AppSettings) => void;
}

const Settings: React.FC<SettingsProps> = ({ settings, onSettingsChange }) => {
  const [form] = Form.useForm();

  const handleSubmit = (values: AppSettings) => {
    onSettingsChange(values);
    message.success('Cài đặt đã được lưu thành công!');
  };

  return (
    <Card className="fade-in-up">
      <div className="flex items-center mb-6">
        <SettingOutlined className="text-3xl mr-3 text-purple-600" />
        <Title level={2} className="mb-0" style={{ color: '#722ed1' }}>Cà<PERSON> đặt hệ thống</Title>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={settings}
        onFinish={handleSubmit}
        className="max-w-md"
      >
        <Form.Item
          label="Số sân mặc định"
          name="courtsCount"
          rules={[
            { required: true, message: 'Vui lòng nhập số sân!' },
            { type: 'number', min: 1, message: 'Số sân phải lớn hơn 0!' }
          ]}
        >
          <InputNumber
            min={1}
            style={{ width: '100%' }}
            placeholder="Nhập số sân mặc định"
          />
        </Form.Item>

        <Form.Item
          label="Số người/sân"
          name="playersPerCourt"
          rules={[
            { required: true, message: 'Vui lòng nhập số người/sân!' },
            { type: 'number', min: 1, message: 'Số người/sân phải lớn hơn 0!' }
          ]}
        >
          <InputNumber
            min={1}
            style={{ width: '100%' }}
            placeholder="Nhập số người/sân"
          />
        </Form.Item>

        <Form.Item
          label="Phí thuê thêm sân (VNĐ)"
          name="extraCourtFee"
          rules={[
            { required: true, message: 'Vui lòng nhập phí thuê thêm sân!' },
            { type: 'number', min: 0, message: 'Phí không được âm!' }
          ]}
        >
          <InputNumber
            min={0}
            step={1000}
            style={{ width: '100%' }}
            placeholder="Nhập phí thuê thêm sân"
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={value => value!.replace(/\$\s?|(,*)/g, '')}
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" size="large" block>
            Lưu cài đặt
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default Settings;
