import React from 'react';

interface CustomLabelProps {
  icon: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

const CustomLabel: React.FC<CustomLabelProps> = ({ icon, children, className = '' }) => {
  return (
    <div className={`custom-label ${className}`}>
      <span className="custom-label-icon">
        {icon}
      </span>
      <span className="custom-label-text">
        {children}
      </span>
    </div>
  );
};

export default CustomLabel;
