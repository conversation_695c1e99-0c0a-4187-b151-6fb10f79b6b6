import React from 'react';
import { <PERSON>, <PERSON>, <PERSON>, Statistic, Alert, Typography } from 'antd';
import { UserOutlined, HomeOutlined, DollarOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { RegistrationSummary } from '../types';

const { Title } = Typography;

interface SummaryProps {
  summary: RegistrationSummary;
}

const Summary: React.FC<SummaryProps> = ({ summary }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  return (
    <Card>
      <div className="flex items-center mb-4">
        <CheckCircleOutlined className="text-2xl mr-2 text-green-600" />
        <Title level={2} className="mb-0">Tổng kết đăng ký</Title>
      </div>

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Tổng số người"
              value={summary.totalPlayers}
              prefix={<UserOutlined />}
              suffix="người"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Số sân cần thiết"
              value={summary.requiredCourts}
              prefix={<HomeOutlined />}
              suffix="sân"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>

        {summary.extraCourts > 0 && (
          <>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="Số sân thêm"
                  value={summary.extraCourts}
                  prefix={<HomeOutlined />}
                  suffix="sân"
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="Số người vượt quá"
                  value={summary.extraPlayersCount}
                  prefix={<UserOutlined />}
                  suffix="người"
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
          </>
        )}
      </Row>

      {summary.totalExtraFee > 0 ? (
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col xs={24} md={12}>
            <Card>
              <Statistic
                title="Tổng phí thêm"
                value={summary.totalExtraFee}
                prefix={<DollarOutlined />}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>

          <Col xs={24} md={12}>
            <Card>
              <Statistic
                title="Phí/người vượt quá"
                value={summary.feePerExtraPlayer}
                prefix={<DollarOutlined />}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>
      ) : (
        <Alert
          message="Không cần thuê thêm sân"
          description="Số người đăng ký vừa đủ với số sân hiện có."
          type="success"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </Card>
  );
};

export default Summary;
