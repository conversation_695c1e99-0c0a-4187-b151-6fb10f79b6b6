export interface AppSettings {
  courtsCount: number;
  playersPerCourt: number;
  extraCourtFee: number;
}

export interface Player {
  id: string;
  name: string;
  registeredAt: Date;
}

export interface WeeklyRegistration {
  id: string;
  weekStart: Date;
  weekEnd: Date;
  players: Player[];
  settings: AppSettings;
}

export interface RegistrationSummary {
  totalPlayers: number;
  requiredCourts: number;
  extraCourts: number;
  extraPlayersCount: number;
  totalExtraFee: number;
  feePerExtraPlayer: number;
}
